((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_56",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A={
cz7(){var y,x
try{y=$.bYx()
return y}catch(x){}return!1}}
A=a.updateHolder(c[92],A)
var z=a.updateTypes([]);(function lazyInitializers(){var y=a.lazyFinal,x=a.lazy
y($,"cJS","bYx",()=>{$.bSu()
return!1})
x($,"cPi","dE",()=>{A.cz7()
return!1})})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_56",e:"endPart",h:b})})($__dart_deferred_initializers__,"K4LyQy0MjiOqvPSxOEiTaOQ0KqE=");