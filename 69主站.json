[{"bookSourceName": "69主站", "bookSourceGroup": "cf盾", "loginUi": "[\n    {\n        name: \"自定义站点\",\n        type: \"text\"\n    }\n]", "bookSourceUrl": "https://69shuba.cx/", "bookSourceType": 0, "bookUrlPattern": "https?:\\/\\/.*69(yuedu|shuba)\\.(cx|com|me|net)\\/(book|article)\\/.*", "customOrder": 0, "enabled": true, "enabledExplore": true, "concurrentRate": "", "header": "@js:\nheaders = {\n\t\"Accept-Language\": \"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7\",\n\t\"Referer\":\"https://69shuba.cx/blist/\",\n\t\"cookie\":\"zh_choose=s\",\n\t\"User-Agent\": java.getWebViewUANEW()\n}\nif(v('lock')||v('img')){\n\theaders[\"X-Requested-With\"] = \"io.bro\"\n\theaders[\"Cache-Control\"] = \"no-cache\"\n}\nJSON.stringify(headers)", "loginUrl": "function login(){\n\tv('Domain',source.getLoginInfoMap().自定义站点)\n}", "loginCheckJs": "", "lastUpdateTime": 0, "weight": 0, "exploreUrl": "@js:\n\nvar sort=[],\npush=(title,surl,size)=>sort.push({\n    title: title,\n    url: surl,\n    style: {\n      layout_flexGrow: 1,\n      layout_flexBasisPercent: size,\n     }\n  });\n\t\npush(\"༺ˇ»`ʚ最近更新ɞ´«ˇ༻\",\"/last\",1,1);\n\n\n[[\"男生\",\"male\"],[\"女生\",\"female\"]].map([title,uri]=>push(title, `blist/${uri}`,  0.4));\n\npush(\"༺ˇ»`ʚ人气ɞ´«ˇ༻\",\"blist/monthvisit_0_0_{{page}}.htm\",1);\n\n\n[[\"连载\",\"monthvisit_0_2_\"],[\"全本\",\"monthvisit_0_1_\"],[\"推荐\",\"allvote_0_0_\"],[\"新书\",\"newhot_0_0_\"]].map([title,uri]=>push(title, `blist/${uri}{{page}}.htm`, 0.2));\n\n\nvar xiaoyu=[\"༺ˇ»`ʚ全部新书ɞ´«ˇ༻\",\"玄幻魔法\",\n\"修真武侠\",\"言情小说\",\"历史军事\",\n\"游戏竞技\",\"科幻空间\",\"悬疑惊悚\",\n\"同人小说\",\"都市小说\",\"官场职场\",\n\"穿越时空\",\"青春校园\"];\nxiaoyu.map((title,i)=>{\n\tlink=`blist/newhot_${i}_0_{{page}}.htm`;\n\tif(i==0){\n\t\tsize=1\n\t\t}else{size=0.25}\n\tpush(title,link,size)\n\t});\n\n\nvar tuomasi=[\"༺ˇ»`ʚ全部人气ɞ´«ˇ༻\",\"玄幻魔法\",\n\"修真武侠\",\"言情小说\",\"历史军事\",\n\"游戏竞技\",\"科幻空间\",\"悬疑惊悚\",\n\"同人小说\",\"都市小说\",\"官场职场\",\n\"穿越时空\",\"青春校园\"];\ntuomasi.map((title,i)=>{\n\tlink=`blist/monthvisit_${i}_0_{{page}}.htm`;\n\tif(i==0){\n\t\tsize=1\n\t\t}else{size=0.25}\n\tpush(title,link,size)\n\t});\n\n\nvar linghu=[\"༺ˇ»`ʚ全部连载ɞ´«ˇ༻\",\"玄幻魔法\",\n\"修真武侠\",\"言情小说\",\"历史军事\",\n\"游戏竞技\",\"科幻空间\",\"悬疑惊悚\",\n\"同人小说\",\"都市小说\",\"官场职场\",\n\"穿越时空\",\"青春校园\"];\nlinghu.map((title,i)=>{\n\tlink=`blist/monthvisit_${i}_2_{{page}}.htm`;\n\tif(i==0){\n\t\tsize=1\n\t\t}else{size=0.25}\n\tpush(title,link,size)\n\t});\n\n\nvar youke=[\"༺ˇ»`ʚ全部全本ɞ´«ˇ༻\",\"玄幻魔法\",\n\"修真武侠\",\"言情小说\",\"历史军事\",\n\"游戏竞技\",\"科幻空间\",\"悬疑惊悚\",\n\"同人小说\",\"都市小说\",\"官场职场\",\n\"穿越时空\",\"青春校园\"];\nyouke.map((title,i)=>{\n\tlink=`blist/monthvisit_${i}_1_{{page}}.htm`;\n\tif(i==0){\n\t\tsize=1\n\t\t}else{size=0.25}\n\tpush(title,link,size)\n\t});\n\nvar nanfeng=[\"༺ˇ»`ʚ全部推荐ɞ´«ˇ༻\",\"玄幻魔法\",\n\"修真武侠\",\"言情小说\",\"历史军事\",\n\"游戏竞技\",\"科幻空间\",\"悬疑惊悚\",\n\"同人小说\",\"都市小说\",\"官场职场\",\n\"穿越时空\",\"青春校园\"];\nnanfeng.map((title,i)=>{\n\tlink=`blist/allvote_${i}_0_{{page}}.htm`;\n\tif(i==0){\n\t\tsize=1\n\t\t}else{size=0.25}\n\tpush(title,link,size)\n\t});\n\n\n\n\n\npush(\"༺ˇ»`ʚ标签ɞ´«ˇ༻\",null,1,1);\ntag=[\"穿越\",\"轻松\",\"系统流\",\"同人衍生\",\"重生\",\"女强\",\"甜宠\",\"1V1\",\"爽文\",\"无敌流\",\"热血\",\"豪门\",\"腹黑\",\"扮猪吃虎\",\"种田\",\"宅斗\",\"日久生情\",\"HE\",\"逆袭\",\"经营\",\"杀伐果断\",\"快节奏\",\"日常文\",\"架空\",\"欢喜冤家\",\"权谋\",\"总裁\",\"打脸\",\"强强\",\"家长里短\",\"萌宝\",\"校园\",\"无限流\",\"智商在线\",\"冷静\",\"空间\",\"宫斗\",\"异能\",\"诸天流\",\"快穿\",\"先婚后爱\",\"脑洞大\",\"帝王\",\"金手指\",\"穿书\",\"大佬\",\"文娱\",\"娱乐圈\",\"赚钱\",\"强者归来\",\"婚恋\",\"明星\",\"青梅竹马\",\"痴情\",\"草根崛起\",\"斗罗大陆\",\"年代文\",\"相爱相杀\",\"兵王\",\"温馨\",\"升级流\",\"爆笑\",\"天才流\",\"美食\",\"洪荒\",\"搞笑\",\"王爷\",\"争霸\",\"游戏异界\",\"星际\",\"霸道\",\"破镜重圆\",\"综漫\",\"谋略\",\"致富\",\"一见钟情\",\"双洁\",\"开局流\",\"时空文\",\"正能量\",\"团宠\",\"护短\",\"学生\",\"马甲\",\"成熟\",\"修真\",\"多女主\",\"智斗\",\"养成\",\"虐渣\",\"单女主\",\"女配\",\"玄学\",\"复仇\",\"双向暗恋\",\"修仙\",\"医生流\",\"励志\",\"王妃\",\"三观正\",\"签到\",\"进化\",\"召唤流\",\"女扮男装\",\"灵气复苏\",\"思路清奇\",\"嫡女\",\"神话\",\"电竞\",\"搞怪\",\"皇后\",\"无节操\",\"护花高手\",\"独宠\",\"古典仙侠\",\"三国\",\"升级\",\"男神\",\"悬疑流\",\"同居\",\"异兽流\",\"乱世\",\"傲娇\",\"隐婚\",\"西游\",\"战争\",\"布局流\",\"神医\",\"废柴流\",\"现代修真\",\"高冷\",\"反套路\",\"武道\",\"萌系\",\"别后重逢\",\"架空历史\",\"明朝\",\"谨慎\",\"偏执\",\"虐文\",\"时空门\",\"探险\",\"唐朝\",\"学霸\",\"未来世界\",\"科举\",\"自律\",\"奋斗\",\"科技\",\"学院流\",\"爱情\",\"吐槽\",\"封神\",\"可盐可甜\",\"校草\",\"直播文\",\"无金手指\",\"朝堂\",\"练功\",\"日系\",\"魔法\",\"军旅\",\"LOL\",\"网游\",\"治愈\",\"强国\",\"异世穿越\",\"御兽流\",\"农门\",\"炼丹\",\"世家\",\"契约婚姻\",\"恋爱\",\"群像\",\"都市修仙\",\"全能\",\"古灵精怪\",\"黑化\",\"朝堂江湖\",\"闪婚\",\"无CP\",\"巧娶\",\"正剧\",\"宠物\",\"克苏鲁\",\"特种兵\",\"凡人流\",\"侦探推理\",\"前世今生\",\"幕后流\",\"强者流\",\"位面\",\"影后\",\"职场商战\",\"随身流\",\"秦汉\",\"超A\",\"悬疑推理\",\"技术流\",\"思想迪化\",\"首席\"];\ntag.map((title,index)=>{\t\t\n\n\tif(index+1 <= tag.length - tag.length%3){\n\t\t\tpush(title, `/blist/tag/${title}/{{page}}/`, 0.25);}\n\t\t\telse{ push(title, `/blist/tag/${title}/{{page}}/`,0.25);}\n\t\t\t});\n\nJSON.stringify(sort);", "ruleExplore": {"bookList": "<EMAIL>||.recentupdate2@ul@li", "name": "h3@text||a.0@text", "author": "class.labelbox@label.0@text", "intro": "class.ellipsis_2@text", "kind": "class.labelbox@label.2@text&&class.labelbox@label.1@text", "lastChapter": "@p.-1@html||a.1@text##.*最近章节.*", "updateTime": "", "bookUrl": "a.0@href", "coverUrl": "a.0@href##.*\\/(\\d+)(\\d{3}).*##https://static.69shuba.com/files/article/image/$1/$1$2/$1$2s.jpg###", "wordCount": ""}, "searchUrl": "@js:\nvar html=\"<!DOCTYPE html>\\n\" +\n    \"<html lang=\\\"en\\\">\\n\" +\n    \"<head>\\n\" +\n    \"    <meta charset=\\\"UTF-8\\\">\\n\" +\n    \"    <title>Title</title>\\n\" +\n    \"</head>\\n\" +\n    \"<body>\\n\" +\n    \"\\n\" +\n    \"<form id=\\\"search\\\" action=\\\"\"+getDomain()+\"/modules/article/search.php\\\" method=\\\"post\\\" accept-charset=\\\"GBK\\\">\\n\" +\n    \"  <input name=\\\"searchkey\\\" type=\\\"text\\\" value=\\\"\"+key+\"\\\">\\n\" +\n    \"</form>\\n\" +\n    \"\\n\" +\n    \"</body>\\n\" +\n    \"<script>\\n\" +\n    \"  var form = document.getElementById('search');\\n\" +\n    \"  form.submit();\\n\" +\n    \"</script>\\n\" +\n    \"</html>\";\nvar re=java.webView(html,getDomain()+\"/blist\",\"\");\nvar base= java.base64Encode(re);\n`data:;base64,${base},{\"type\":\"qread\"}`;", "ruleSearch": {"bookList": "<js>\nresult=java.hexDecodeToString(result)\nif(result.match('为了获得最佳体验，请使用新版本的Google Chrome 或者其他主流浏览器访问此网站')){\n    result=java.startBrowserAwait(getDomain()+\"/modules/article/search.php\",\"验证\",false)\n}\n</js>\n.newbox@ul@li||.newlistbox>ul>li||body>li||.recentupdate2>ul>li", "name": ".newnav@h3@text", "author": "label.0@text", "intro": "ol@text", "kind": "label.1:2@text||span@text", "lastChapter": "(.zxzj>p@ownText&&.zxzj>span@text)||a.1@text##\\n##·\n@js:result\n.replace(\"••\",\"\")\n.replace(/^(\\d+).第/,'第')\n.replace(/^(\\d+)[、．]第.+章/,'第$1章')\n.replace(/^(\\d+)、\\d+、/,'第$1章 ')\n.replace(/^(\\d+)、\\d+/,'第$1章')\n.replace(/^(第.+章)\\s?\\d+/,'$1')\n.replace(/^(\\d+)、/,'第$1章 ')\n.replace(/^(第.+章)\\s?第.+章/,'$1')\n.replace(/第\\s(.+)\\s章/,'第$1章')\n.replace(/.*(chapter|Chapter)\\s?(\\d+)\\s?/,'$1 $2 ')\n.replace(/\\(.+\\)/,'')\n.replace(/\\[|。/,'')\n.replace(/第([零一二两三四五六七八九十百千]+)章/g,java.toNumChapter(result))\n##(章)([^\\s]+)(\\s·)##$1 $2$3", "updateTime": "", "bookUrl": "a.0@href##\\.me##.com", "coverUrl": "<js>fixImg(java.getString('img@data-src||img@src'), completeUrl(java.getString('a.0@href')))\n</js>##.*\\/(\\d+)(\\d{3}).*##https://static.69shuba.com/files/article/image/$1/$1$2/$1$2s.jpg###", "wordCount": "", "checkKeyWord": "我的模拟长生路"}, "ruleBookInfo": {"init": ".bookbox,.tabsnav,.jianjie-popup@js:\nbookUrl = String(java.getRedirectUrl())\nbook.setBookUrl(bookUrl)\nlet toc = java.getString('.more-btn@href');if(toc==''){let read = String(java.getString('.btn.0@href')).replace(/https?:\\/\\/.*?(?=\\/)/).split('/');let len=read.length;if(len>3){if(/\\d$/.test(read)){toc = bookUrl.replace(/\\.[^\\/]*$/,'/')}else{let tp=read[len-1].match(/\\.[^.\\/]+$/);java.put('path', '/'+read[1]+'/');java.put('tp', tp?tp[0]:'');toc = String(bookUrl).replace(/.html?$/,'.json')}}else{toc = read.join('/')}}java.put('toc',toc);tags=src.match(/tags\\s*:\\s*['\"](.*)['\"]/);if(tags&&tags[1]!='')java.put('tags','🏷 标签：'+tags[1].replace(/\\|$/,'')+'\\n📜 简介：');result", "name": "h1@text", "author": "p.0@text", "intro": "<br>@get:{tags}{{@@.navtxt>p.0@html||.jianjie-popup .content@html}}\n<js>##\\|##·</js>", "kind": "p[1:3]@text##.+：\n<js>##\\s\\|\\s##,</js>", "lastChapter": "li.0@span@text&&li.0@small@text##\\n##•\n@js:result\n.replace(\"••\",\"\")\n.replace(/^(\\d+).第/,'第')\n.replace(/^(\\d+)[、．]第.+章/,'第$1章')\n.replace(/^(\\d+)、\\d+、/,'第$1章 ')\n.replace(/^(\\d+)、\\d+/,'第$1章')\n.replace(/^(第.+章)\\s?\\d+/,'$1')\n.replace(/^(\\d+)、/,'第$1章 ')\n.replace(/^(第.+章)\\s?第.+章/,'$1')\n.replace(/第\\s(.+)\\s章/,'第$1章')\n.replace(/.*(chapter|Chapter)\\s?(\\d+)\\s?/,'$1 $2 ')\n.replace(/\\(.+\\)/,'')\n.replace(/\\[|。/,'')\n.replace(/第([零一二两三四五六七八九十百千]+)章/g,java.toNumChapter(result))\n##(章)([^\\s]+)(\\s·)##$1 $2$3", "updateTime": "", "coverUrl": "<js>fixImg(java.getString('img@data-src||img@src'), completeUrl(java.getString('a.0@href')))\n</js>##.*\\/([\\d]{2})(\\d+).*##https://static.69shuba.com/files/article/image/$1/$1$2/$1$2s.jpg###", "tocUrl": "@get:{toc}@js:completeUrl(result, book.bookUrl)", "wordCount": "", "canReName": ""}, "ruleToc": {"preUpdateJs": "", "chapterList": "@js:\nlet o = java.getElement('.catalog:last-child>ul>li')\nlet aid = java.getString('aid')\nif(aid!=''){\n\tlet items = java.getElement('items')\n\to = []\n\tfor(v of Object.values(items)){\n\t\tlet href = java.get('path')+aid+'/'+v.cid+java.get('tp')\n\t\thref = completeUrl(href, baseUrl)\n\t\to.push({text:v.n,href:href})\n\t}\n}else{\n\tlet use = {}, offset = 0\n\tArray.from(o).filter(v=>v.hasAttr('data-num')).sort((a,b)=>a.attr('data-num')-b.attr('data-num')).map((v, i)=>{if(!use[v.attr('data-num')]){o[i-offset]=v.select('a')[0];use[v.attr('data-num')]=true}else{o.remove(i-offset);offset++}})\n}o", "chapterName": "text##^\\d+\\.|（.*合.*|（含.*|\\(.*合.*|\\(含.*\n@js:result\n.replace(\"••\",\"\")\n.replace(/^(\\d+).第/,'第')\n.replace(/【\\d+】/,'')\n.replace(/^(正文|VIP章节|最新章节)?(\\s+|_)|[\\(\\{（｛【].*[求含理更谢乐发推票盟补加字Kk\\/].*/g,'')\n.replace(/^(\\d+)[、．]第.+章/,'第$1章')\n.replace(/^(\\d+)、\\d+、/,'第$1章 ')\n.replace(/^(\\d+)、\\d+/,'第$1章')\n.replace(/^(第.+章)\\s?\\d+/,'$1')\n.replace(/^(\\d+)、/,'第$1章 ')\n.replace(/^(第.+章)\\s?第.+章/,'$1')\n.replace(/第\\s(.+)\\s章/,'第$1章')\n.replace(/.*(chapter|Chapter)\\s?(\\d+)\\s?/,'$1 $2 ')\n.replace(/\\(.+\\)/,'')\n.replace(/\\[|。/,'')\n.replace(/第([零一二两三四五六七八九十百千]+)章/g,java.toNumChapter(result))\n##(章)([^\\s]+)(\\s·)##$1 $2$3", "chapterUrl": "href\n@js:completeUrl(result, baseUrl)", "isVolume": "", "isVip": "", "isPay": "", "wordCount": "", "updateTime": "", "nextTocUrl": ""}, "ruleContent": {"content": "<js>\nif(result.match(/Just a moment/)){\n\tjava.startBrowserAwait(baseUrl,\"验证\");\n\tresult=java.ajax(baseUrl);\n}result;\n</js>\n.txtnav@html||.content@html##<h1[\\s\\S]+?</h1>|^\\s*<[a-z]+ .*>|<\\/[a-z]+>$|(?<!^)<div[\\s\\S]*?</div>\\s*", "nextContentUrl": "", "title": "h1@text##正文卷.|正文.|VIP卷.|默认卷.|卷_|VIP章节.|免费章节.|章节目录.|最新章节.|[\\(（【].*?[求含理更谢乐发推票盟补加字].*?[】）\\)]\n@js:result\n.replace(\"••\",\"\")\n.replace(/^(\\d+).第/,'第')\n.replace(/^(\\d+)[、．]第.+章/,'第$1章')\n.replace(/^(\\d+)、\\d+、/,'第$1章 ')\n.replace(/^(\\d+)、\\d+/,'第$1章')\n.replace(/^(第.+章)\\s?\\d+/,'$1')\n.replace(/^(\\d+)、/,'第$1章 ')\n.replace(/^(第.+章)\\s?第.+章/,'$1')\n.replace(/第\\s(.+)\\s章/,'第$1章')\n.replace(/.*(chapter|Chapter)\\s?(\\d+)\\s?/,'$1 $2 ')\n.replace(/\\(.+\\)/,'')\n.replace(/\\[|。/,'')\n.replace(/第([零一二两三四五六七八九十百千]+)章/g,java.toNumChapter(result))\n##(章)([^\\s]+)(\\s·)##$1 $2$3", "webJs": "", "sourceRegex": "", "payAction": "", "replaceRegex": "##\\s*\\(本章完\\)\\s*$|第.*章.*", "imageStyle": ""}, "bookSourceComment": "//使用需要改包名\n//By情无羁(yesui.me)25.01.25修改部分规则,个人用法：点登录后选择强制保持站点然后打钩，搜索后过cf盾显示列表为0后重新搜索即可\n25.02.16修改部分规则，修改发现规则\n25.02.20修复封面\n25.03.04完善规则\n25.03.13绕过me域名验证\n25.03.26完善发现(感谢Buding)\n25.03.30修复封面\n25.04.08修复封面\n靈狐 制，可拷贝书源，更改源URL当不同源用\n书架书籍与源URL绑定，如站点变动可在登录里自定义站点来避免重新添加书架\n24-05-13 修复乱序\n24-06-04 发现与更多模板支持\n理论支持全部69网站使用，发现若错乱请刷新\n24-07-02 支持更多模板，部分站点只有排行榜，暂不予适配发现\n24-07-13 目录排序，支持无标记正文排序，支持设置源变量改变搜索、发现\n24-07-18 发现支持筛选、单页，源变量设置可改变所有页面，优化正文标题净化\n24-09-18 支持移除目录重复条目，详情页添加标签，移除正文解密、排序，登录进行源站相关设置（强制保持：强制替换站点为当前站点），添加过cf盾\n24-09-20 避免因Cookie丢失导致页面被缓存无法过盾，可设置封面为69默认封面\n24-09-21 添加新的封面链接，加入随机UA功能\n24-09-28 修复站点目录，发现添加最近更新（如果有），封面修复改良\nhttps://www.69shu.cx\nhttps://www.69shuba.pro\nhttps://www.69yuedu.net\nhttps://69shuba.cx\nhttps://69shu.ac", "respondTime": 180000, "jsLib": "function v(key, value, del){\n\tconst {source} = this\n\tlet data = source.variable\n\ttry{data = JSON.parse(data)}catch(e){data = {}}\n\tif(!!value||value==0)data[key] = value\n\tif(del)delete data[key]\n\tsource.variable = JSON.stringify(data)\n\treturn data[key]\n}\nfunction cf(url, times){\n\tconst {cache, java, cookie} = this\n\tlet f = (n,v,d) => this.v(n,v,d), result, id = (Math.random()+0.1)*10, times = times?times:0, yz = url\n\tif(/search.php,/.test(yz))yz = completeUrl.call(this, '/',yz)\n\ttry{\n\t\tif(!f('ok')&&!f('lock')){\n\t\t f('lock', id)\n\t\t java.log(id)\n\t\t if(f('lock') == id){\n\t\t \tresult = java.startBrowserAwait(yz,\"验证\"), e()\n\t\t }\n\t }\n\t}catch{f('ok', 1);f('lock', null, 1)}\n\twhile(f('lock')){}\n\tif(yz!=url||!result||result.body().match('_cf_')){\n\t\tresult = java.connect(url)\n\t\tif(result.body().match('_cf_')){\n\t\t\tf('ok', null, 1)\n\t\t\tif(times==0)result = cf.call(this, url, times+1)\n\t\t}\n\t}\n\treturn result\n}\nfunction fixImg(img, url){\n\tconst {java} = this\n\tif(!v.call(this, 'f')&&!/\\ds\\.jpg$/.test(img)){\n\t\tlet m=url.match(/\\/(\\d+)(\\.[^.]*)?$/)\n\t\tif(!m)return img\n\t\timg = this.completeUrl('/bimages/'+Math.floor(m[1]/1000)+'/'+m[1]+'/'+m[1]+'s.jpg', url)\n\t}\n\treturn img\n}\nfunction randomUa(){\n\tlet r = (a,b) => Math.floor(Math.random()*(b-a)+a), x = l =>{for(i=0,s='';i<l;i++){s+=r(0,10)}return s}, v = r(500, 700)+'.'+x(2)\n\treturn `Mozilla/5.0 (Linux; Android ${r(5,15)}; wv) AppleWebKit/${v} (KHTML, like Gecko) Chrome/${r(76,122)}.0.${x(4)}.${x(2)} Mobile Safari/${v}`\n}\nfunction getDomain(){\n\tconst {source} = this\n\tlet domain = v.call(this, 'Domain')\n\tif(!domain||domain=='')domain=source.key\n\treturn domain\n}\nfunction completeUrl(url, base) {\n  let baseUrl = base || getDomain.call(this)\n  if(v.call(this,'k'))url = String(url).replace(/.*\\/\\/[^/]+/, '')\n  let m = baseUrl.match(/(.+?)\\/\\/([^/]+)(.*\\/?)(.*)/)\n  let fill = ''\n  if (url.match(/^https?:/)) {\n  } else if (url.match(/^\\/\\//)) {\n    fill = m[1]\n  } else {\n    fill = m[1] + '//' + m[2]\n    if (!url.match(/^\\//)){\n    \tfill = fill + m[3]\n    }\n  }\n  return fill + url\n}", "enabledCookieJar": true, "userid": "", "variableComment": ""}]