solon.dataSources:
  db:
    class: "com.zaxxer.hikari.HikariDataSource"
    driverClassName: com.p6spy.engine.spy.P6SpyDriver
    jdbcUrl: *************************
    maxPoolSize: 1
admin:
  username: "admin"
  password: "adminadmin"
  #code: "接口的code参数" //如果需要使用获取邀请码参数请修改这个参数，重启生效
user:
  allowuptxt: true  #是否允许上传txt 允许 true 不允许 false
  allowcache: true  #是否允许添加缓存 允许 true 不允许 false

smtp:
  host:  #smtp邮箱host
  protocols: TLSv1.2 #通讯协议
  port:  #端口
  account:  #邮箱账号
  password:  #邮箱密码，部分邮箱是安全码
  personal:  #发送人的昵称
  codesubject: 验证码 #验证码邮件的主题
  codetext: 您的验证码是$code,当前验证码十分钟有效请尽快使用  #验证码邮件的内容，必须有$code，发送邮件时会把$code替换成验证码
default:
  tts: #默认tts链接
  rule: #默认净化链接
