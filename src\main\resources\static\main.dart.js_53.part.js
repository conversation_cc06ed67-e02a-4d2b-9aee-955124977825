((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_53",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A,B,C,D={
aB4(){var x=0,w=A.i(y.f),v,u
var $async$aB4=A.d(function(d,e){if(d===1)return A.e(e,w)
while(true)switch(x){case 0:$.bYv()
x=3
return A.c(C.Iw.bQ("getApplicationDocumentsDirectory",null,!1,y.g),$async$aB4)
case 3:u=e
if(u==null)throw A.k(B.c2T("Unable to get application documents directory"))
v=B.uY(u)
x=1
break
case 1:return A.f(v,w)}})
return A.h($async$aB4,w)}}
A=c[0]
B=c[75]
C=c[116]
D=a.updateHolder(c[71],D)
var z=a.updateTypes([])
var y={f:A.G("nn"),g:A.G("j")}};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_53",e:"endPart",h:b})})($__dart_deferred_initializers__,"7XDbJbH3V/nyv1HzawNkmCXNIg8=");