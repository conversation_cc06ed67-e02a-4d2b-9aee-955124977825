((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_25",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var J,A,B={
D8(){var x=0,w=A.i(y.b),v=1,u=[],t,s,r,q,p
var $async$D8=A.d(function(d,e){if(d===1){u.push(e)
x=v}while(true)switch(x){case 0:v=3
s=C.bXP()
$.cmG=s
p=$
x=6
return A.c(s.p7(0,"ImgStorage",new B.aSY(),1),$async$D8)
case 6:p.yR=e
v=1
x=5
break
case 3:v=2
q=u.pop()
t=A.E(q)
A.W("IndexedDB \u521d\u59cb\u5316\u5931\u8d25: "+A.m(t))
x=5
break
case 2:x=1
break
case 5:return A.f(null,w)
case 1:return A.e(u.at(-1),w)}})
return A.h($async$D8,w)},
aSY:function aSY(){}},C
J=c[1]
A=c[0]
B=a.updateHolder(c[61],B)
C=c[74]
var z=a.updateTypes(["aY(Az)"])
B.aSY.prototype={
$1(d){var x=d.gSi(d)
J.aBC(x,"storage")},
$S:z+0};(function inheritance(){var x=a.inherit
x(B.aSY,A.cX)})()
var y={b:A.G("~")};(function staticFields(){$.cmG=null
$.yR=null})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_25",e:"endPart",h:b})})($__dart_deferred_initializers__,"PGghFBwQY1/kNBsO7ds/rCEwbPk=");