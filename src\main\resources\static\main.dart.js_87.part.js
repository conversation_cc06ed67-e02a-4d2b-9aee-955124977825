((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_87",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var B,D,A={
bXs(d,e,f){var y,x,w,v,u,t,s=e.b
if(s<=0||e.a<=0||f.b<=0||f.a<=0)return C.a0t
switch(d.a){case 0:y=f
x=e
break
case 1:w=f.a
v=f.b
u=e.a
y=w/v>u/s?new B.U(u*v/s,v):new B.U(w,s*w/u)
x=e
break
case 2:w=f.a
v=f.b
u=e.a
x=w/v>u/s?new B.U(u,u*v/w):new B.U(s*w/v,s)
y=f
break
case 3:w=f.a
v=f.b
u=e.a
if(w/v>u/s){x=new B.U(u,u*v/w)
y=f}else{y=new B.U(w,s*w/u)
x=e}break
case 4:w=f.a
v=f.b
u=e.a
if(w/v>u/s){y=new B.U(u*v/s,v)
x=e}else{x=new B.U(s*w/v,s)
y=f}break
case 5:x=new B.U(Math.min(e.a,f.a),Math.min(s,f.b))
y=x
break
case 6:t=e.a/s
w=f.b
y=s>w?new B.U(w*t,w):e
s=f.a
if(y.a>s)y=new B.U(s,s/t)
x=e
break
default:x=null
y=null}return new A.aag(x,y)},
xp:function xp(d,e){this.a=d
this.b=e},
aag:function aag(d,e){this.a=d
this.b=e}},C
B=c[0]
D=c[2]
A=a.updateHolder(c[81],A)
C=c[123]
A.xp.prototype={
L(){return"BoxFit."+this.b}}
A.aag.prototype={}
var z=a.updateTypes([]);(function inheritance(){var y=a.inherit
y(A.xp,B.fM)
y(A.aag,B.w)})();(function constants(){C.ke=new A.xp(0,"fill")
C.cZ=new A.xp(1,"contain")
C.bo=new A.xp(2,"cover")
C.he=new A.xp(3,"fitWidth")
C.hf=new A.xp(4,"fitHeight")
C.o_=new A.xp(6,"scaleDown")
C.a0t=new A.aag(D.N,D.N)})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_87",e:"endPart",h:b})})($__dart_deferred_initializers__,"PIvZ48miEHXHVR9AGLO+Mw8atS0=");