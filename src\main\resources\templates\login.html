<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>登录</title>
</head>
<link rel="stylesheet" href="/static/layui/css/layui.css">
<link rel="stylesheet" href="/static/css/login.css">
<body style="background-color: #F3F3F4">
<div class="login-main">
    <header class="layui-elip">登录</header>
    <form class="layui-form">
        <div class="layui-input-inline">
            <input type="text" name="username" required lay-verify="required" placeholder="用户名" autocomplete="off"
                   class="layui-input">
        </div>
        <div class="layui-input-inline">
            <input type="password" name="password" required lay-verify="required" placeholder="密码" autocomplete="off"
                   class="layui-input">
        </div>
        <div class="layui-input-inline login-btn">
            <button lay-submit lay-filter="login" class="layui-btn">登录</button>
        </div>

        <!--<div class="layui-input-inline">
            <button type="button" class="layui-btn layui-btn-primary">QQ登录</button>
        </div>
        <div class="layui-input-inline">
            <button type="button" class="layui-btn layui-btn-normal">微信登录</button>
        </div>-
        <p><a href="register.html" class="fl">立即注册</a><a href="javascript:;" class="fr">忘记密码？</a></p>-->
    </form>
</div>
</body>


<script src="/static/layui/layui.js"></script>
<script src="/static/js/jquery-ui.min.js"></script>
<script src="/static/js/ajax.js"></script>
<script>

    layui.use(['form','layer'], function () {
        $ = layui.jquery;
        // 操作对象
        var form = layui.form;
        form.on('submit(login)',function (data) {
            console.log(data.field);
            ajax("POST","/admin/login",data.field,function (data) {
                if (data.isSuccess == true){
                    location.href = "/admin";
                }else{
                    layer.msg(data.errorMsg);
                }
            })

            return false;
        })

    });
</script>

</html>