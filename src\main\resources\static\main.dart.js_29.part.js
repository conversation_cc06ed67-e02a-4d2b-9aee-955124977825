((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_29",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var B,A={
cwq(){throw B.k(B.aM("Platform._operatingSystem"))},
cwt(){return A.cwq()}}
B=c[0]
A=a.updateHolder(c[100],A)
var z=a.updateTypes([]);(function lazyInitializers(){var y=a.lazyFinal
y($,"cJV","bSu",()=>A.cwt())})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_29",e:"endPart",h:b})})($__dart_deferred_initializers__,"wZWIjtsX1ojc/oOjc3/8wh7D/Ag=");