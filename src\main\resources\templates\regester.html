<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>注册</title>
</head>
<link rel="stylesheet" href="/static/layui/css/layui.css">
<link rel="stylesheet" href="/static/css/login.css">
<body style="background-color: #F3F3F4">
<div class="login-main">
   <br><br>
    <form class="layui-form layui-form-pane">

        <div class="layui-form-item" >
            <label class="layui-form-label">用户名</label>
            <div class="layui-input-block">
                <input type="text" name="username" autocomplete="off" placeholder="请输入用户名" lay-verify="required" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" >
            <label class="layui-form-label">密码</label>
            <div class="layui-input-block">
                <input type="password" name="password" autocomplete="off" placeholder="请输入密码" lay-verify="required" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" >
            <label class="layui-form-label">密码</label>
            <div class="layui-input-block">
                <input type="password" name="password2" autocomplete="off" placeholder="请输入再次密码" lay-verify="required" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" >
            <label class="layui-form-label">邮箱</label>
            <div class="layui-input-block">
                <input type="text" name="email" autocomplete="off" placeholder="请输入邮箱" lay-verify="required" class="layui-input">
            </div>
        </div>
        <!--<div class="layui-form-item" >
            <label class="layui-form-label">手机号</label>
            <div class="layui-input-block">
                <input type="text" name="phone" autocomplete="off" placeholder="请输入手机号" lay-verify="required" class="layui-input">
            </div>
        </div>-->
        <div class="layui-form-item" >
            <label class="layui-form-label">邀请码</label>
            <div class="layui-input-block">
                <input type="text" name="code" autocomplete="off" placeholder="请输入邀请码" lay-verify="required" class="layui-input">
            </div>
        </div>
        <div class="layui-input-inline login-btn" >
            <button type="submit" lay-submit lay-filter="sub" class="layui-btn" lay->注册</button>
        </div>
        <br/>
    </form>
</div>


</body>
<script src="/static/layui/layui.js"></script>
<script src="/static/js/jquery-ui.min.js"></script>
<script src="/static/js/ajax.js"></script>
<script>

    layui.use(['form','layer'], function () {
        $ = layui.jquery;
        // 操作对象
        var form = layui.form;
        form.on('submit(sub)',function (data) {
            console.log(data.field);
            var field = data.field;
            if(field.password!=field.password2){
                layer.msg("两次密码不一致");
                return false;
            }
            ajax("POST","/regester",data.field,function (data) {
                if (data.isSuccess == true){
                    layer.msg("注册成功");
                }else{
                    layer.msg(data.errorMsg);
                }
            })

            return false;
        })

    });
</script>

</html>