((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_100",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A={
ly(d,e,f,g,h,i,j,k,l,m){return new B.pU(k,i,j,l,d,e,m,g,h,f)}},B
A=a.updateHolder(c[64],A)
B=c[80]
var z=a.updateTypes([])};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_100",e:"endPart",h:b})})($__dart_deferred_initializers__,"Qpr0CTNL5i1rEu3ImfeCYbX9af4=");