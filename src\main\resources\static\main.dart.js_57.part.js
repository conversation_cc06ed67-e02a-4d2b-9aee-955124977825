((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_57",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A,B={
czk(){var y,x
try{}catch(x){y=A.E(x)
A.W(A.m(y))}return!1}}
A=c[0]
B=a.updateHolder(c[98],B)
var z=a.updateTypes([]);(function lazyInitializers(){var y=a.lazy
y($,"cPk","dm",()=>{B.czk()
return!1})})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_57",e:"endPart",h:b})})($__dart_deferred_initializers__,"Zt79BDnR7gmVlaTcLgRZTFSxnYQ=");