<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="read">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>read</title>
  <!-- Loading indicator -->
  <style>
    .container{
        width: 100vw;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        flex: auto;
        flex-direction: column;
    }
    .indicator{
        width: 100px;
        height: 100px;
        margin-bottom: 15px;
    }
    .progress-bar {
        width: 100px;
        background-color: #f3f3f3;
        border-radius: 4px;
    }
    @keyframes progress {
        0% { width: 50%; }
        100% { width: 100%; }
    }
    .progress-bar-fill {
        display: block;
        height: 5px;
        background-color: #46494b;
        border-radius: 4px;
        transition: width 0.2s ease-in;
        animation: progress 5s ease-in-out forwards;
    }
  </style>
  <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="loading_indicator" class="container">
  <img class="indicator" src="/icons/app_icon.png"/>
  <div class="progress-bar">
    <div class="progress-bar-fill" style="width: 50%;"></div>
  </div>
</div>
<script>
  window.addEventListener('load', function(ev) {
      var loading = document.getElementById('loading_indicator');
      _flutter.loader.loadEntrypoint({
          serviceWorker: {
              serviceWorkerVersion: serviceWorkerVersion,
          },
          onEntrypointLoaded: async function(engineInitializer) {
              let appRunner = await engineInitializer.initializeEngine();
              await appRunner.runApp();
              window.setTimeout(function () {
                  loading.remove();
              }, 200);
          }
      });
  });
</script>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
