package web.response

val NEED_LOGIN = "NEED_LOGIN"

val NOT_BANK = "NOT_BANK"

val PASS_ERROR = "用户名或密码错误"

val EMAIL_CHECK_ERROR = "邮箱填写错误"

val EMAIL_IS = "邮箱已存在"

val NOT_SOURCE = "书源不存在"

val IS_WEBVIEW = "webview书源不支持缓存"

val SUCCESS = "success"

val BOOKSEARCHERROR="通过书源获取书本信息失败"

val BOOKIS="书本已在书架"

val CacheIS="当前书本已在缓存进程中"

val GROUPIS="分组已存在"

val NO_BOOK="书本不存在"

val NO_PAY="购买操作不存在"

val NOT_ALLOW_TXT ="不允许导入图书"

val NOT_TXT ="当前文件格式不支持"

val PASS_VAIL_ERROR="密码长度请在6位到15位之间"

val EMAIL_ERROR="邮箱格式不正确"

val PHONE_ERROR="手机号不正确"

val USER_IS="用户名已存在"

val USER_NOT="用户不存在"

val NOT_IS="不存在"

val MAX_ERROR="书源或者订阅源超出最大数量"

val USE_ERROE="错误使用"

val COOKIE_IS="cookie已存在，请删除后重新添加"

val USERNAME_NOT_CHANGE="不允许修改用户名"

val SOURCE_URL_BANK="书源链接不能为空"

val SOURCE_TYPE_ERROR="暂不支持非文本连接"

val JSON_ERROR="json格式化错误"

val DO_ERROR="导入失败"

val NUM_ERROR="最小不能小于0"

val ADD_ERROR="添加失败"

val CODE_ERROR="邀请码不存在"

val CAN_NOT="无权限"

val SOURCE_JSON_ERROR="书源格式化错误"

val SOURCE_URL_ERROR="书源URL不呢为空"

val SOURCE_IS = "书源已存在"

val CACHE_ERROR="最多5个缓存任务"

val CODE_CHECK_ERROR= "验证码错误"

val NAME_ERROR="名字重复"

val MARK_IS="书签已存在"