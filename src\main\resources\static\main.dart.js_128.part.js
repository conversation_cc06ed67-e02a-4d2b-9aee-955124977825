((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_128",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A,B
A=c[0]
B=c[132]
var z=a.updateTypes([]);(function constants(){B.i3=new A.bD(16,16)})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_128",e:"endPart",h:b})})($__dart_deferred_initializers__,"u++q7Kr076x96MnA1ZuiG/eB9M8=");