package android.os

object Build {

    const val BOARD = "kona"
    const val BOOTLOADER = "OPPO.2025.07"
    const val BRAND = "OPPO"
    const val DEVICE = "OP50"
    const val DISPLAY = "CPH2447_13.1.0.501(EX01)"
    const val FINGERPRINT = "OPPO/OP50/OP50:13/TP1A.220624.014/1675062000:user/release-keys"
    const val HARDWARE = "qcom"
    const val HOST = "oppo-build"
    const val ID = "TP1A.220624.014"
    const val MANUFACTURER = "OPPO"
    const val MODEL = "OP50"
    const val PRODUCT = "OP50"
    const val TAGS = "release-keys"
    const val TYPE = "user"
    const val USER = "oppo"
    const val TIME = 1735660800000L // 2025年1月1日

    object VERSION {
        const val BASE_OS = ""
        const val CODENAME = "Tiramisu"
        const val INCREMENTAL = "1675062000"
        const val PREVIEW_SDK_INT = 0
        const val RELEASE = "13"
        const val SDK = "33"
        const val SDK_INT = 33
        const val SECURITY_PATCH = "2025-07-05"
    }
}