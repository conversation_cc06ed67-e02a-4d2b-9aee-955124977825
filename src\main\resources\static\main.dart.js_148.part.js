((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_148",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A={
baF(d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t){return new B.Ll(f,h,i,d,e,j,k,l,s,t,n,p,m,q,r,g,o)}},B
A=a.updateHolder(c[38],A)
B=c[99]
var z=a.updateTypes([])};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_148",e:"endPart",h:b})})($__dart_deferred_initializers__,"GE8Ji6K/GnUNumtN6hbaAKN0lIc=");