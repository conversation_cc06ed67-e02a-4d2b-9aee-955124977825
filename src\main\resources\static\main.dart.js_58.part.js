((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_58",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var B,A={
cEF(d){var x=document
x.toString
B.bWl(x,"keydown",new A.bRY(d),!1,y.c)},
bRY:function bRY(d){this.a=d},
cgJ(d){return A.cEF(d)}}
B=c[0]
A=a.updateHolder(c[15],A)
var z=a.updateTypes([])
A.bRY.prototype={
$1(d){var x=d.keyCode
x.toString
if(x===27)this.a.$0()},
$S:191};(function inheritance(){var x=a.inherit
x(A.bRY,B.cX)})()
var y={c:B.G("vi")}};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_58",e:"endPart",h:b})})($__dart_deferred_initializers__,"C6zRjelKBeNrtUmjzKGGc7qI6mo=");