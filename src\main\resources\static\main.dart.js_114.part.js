((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_114",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A={
c4J(){var y=$.br
B.dk(y==null?null:y.cV(0,"imgmax"))
return 5},
c4M(){var y=$.br
y=y==null?null:y.cV(0,"turn")
return B.dk(y==null?0:y)},
c4I(){if(!$.dE())return!1
var y=$.br
y=y==null?null:y.cV(0,"setting:fontSizevalue")
return y==="true"}},C,B,D
A=a.updateHolder(c[43],A)
C=c[136]
B=c[94]
D=c[99]
var z=a.updateTypes([]);(function constants(){C.pt=new D.bk(58121,"MaterialIcons",!0)})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_114",e:"endPart",h:b})})($__dart_deferred_initializers__,"n/6D/Eo4FntGO++p3VHJ02BQAfw=");