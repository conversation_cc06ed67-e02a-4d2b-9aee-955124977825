<!DOCTYPE html>
<html lang="en">
<head>
    <meta char后台set="UTF-8">
    <title>管理</title>
</head>
<link rel="stylesheet" href="/static/layui/css/layui.css">

<body style="width: 100%;height: 100%;">
<div style="margin-left: 8%;margin-right: 8%; margin-top: 40px">

    <form class="layui-form" action="" lay-filter="formTest">
        <div class="layui-form-item">
            <label class="layui-form-label">用户名</label>
            <div class="layui-input-block">
                <input type="text" name="username"   id="username" lay-verify="required" placeholder="请输入用户名" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" id="p1"  >
            <label class="layui-form-label">密码</label>
            <div class="layui-input-block">
                <input type="text" name="password"   placeholder="请输入密码" lay-verify="required" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item"  id="p2">
            <label class="layui-form-label">密码</label>
            <div class="layui-input-block">
                <input type="text" name="password"   placeholder="留空则不修改" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" >
            <label class="layui-form-label">邮箱</label>
            <div class="layui-input-block">
                <input type="text" name="email"  placeholder="请输入邮箱"  class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" >
            <label class="layui-form-label">手机号</label>
            <div class="layui-input-block">
                <input type="text" name="phone"  placeholder="请输入手机号" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
             <label class="layui-form-label">上传txt</label>
             <div class="layui-input-block">
               <select name="AllowUpTxt" lay-filter="al">
                 <option value="1">是</option>
                 <option value="0">否</option>
               </select>
             </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">图片解密</label>
            <div class="layui-input-block">
                <select name="AllowImg" lay-filter="al4">
                    <option value="1">是</option>
                    <option value="0">否</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">书源检验</label>
            <div class="layui-input-block">
                <select name="Allowcheck" lay-filter="al5">
                    <option value="1">是</option>
                    <option value="0">否</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">书源</label>
            <div class="layui-input-block">
                <select name="source" lay-filter="al3">
                    <option value="0">无法修改</option>
                    <option value="1">允许修改</option>
                    <option value="2">独立书源</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">缓存书本</label>
            <div class="layui-input-block">
                <select name="AllowCache" lay-filter="al2">
                    <option value="1">允许</option>
                    <option value="0">不允许</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item" >
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <input type="text" name="comment"  placeholder="请输入备注" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="ti">立即提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>

<script src="/static/layui/layui.js"></script>
<script src="/static/js/ajax.js"></script>
<script>
    var $
    layui.use(['jquery','form'], function () {
        var form =layui.form
        $ = layui.$
        const searchParams = new URLSearchParams(window.location.search);
        var oid = searchParams.get('id')

        if(oid == null || oid == "null"){
            oid = ""
            var data={"AllowCache":0,"AllowUpTxt":0,"AllowImg":0,"Allowcheck":0}
            form.val("formTest",data);
        }
        if ( oid != ""){
            $("#p1").remove()
            $("#username").attr("disabled","disabled");
            ajax("POST","/admin/getuser?id="+oid,{},function (data) {
                if (data.isSuccess == true){
                    if(data.data.AllowUpTxt){
                        data.data.AllowUpTxt = 1
                    }else {
                        data.data.AllowUpTxt = 0
                    }
                    if(data.data.AllowImg){
                        data.data.AllowImg = 1
                    }else {
                        data.data.AllowImg = 0
                    }
                    if(data.data.Allowcheck){
                        data.data.Allowcheck = 1
                    }else {
                        data.data.Allowcheck = 0
                    }

                    if(data.data.AllowCache){
                        data.data.AllowCache = 1
                    }else {
                        data.data.AllowCache = 0
                    }
                    //console.log(data.data)
                    form.val("formTest",data.data);
                }else{
                    layer.msg(data.errorMsg);
                }
            })
        }else {
            $("#p2").remove()
        }

        var index = parent.layer.getFrameIndex(window.name);
        form.on('submit(ti)', function (data) {
            if (oid != ""){
                data.field.id=oid
            }
            ajax("POST","/admin/adduser",data.field,function (data) {
                if (data.isSuccess == true){
                    //console.log(data.data)
                    parent.layer.msg("更新成功");
                    parent.shua();
                    parent.layer.close(index);
                }else{
                    layer.msg(data.errorMsg);
                }
            })
            return false
        });

    })
</script>
</body>
</html>