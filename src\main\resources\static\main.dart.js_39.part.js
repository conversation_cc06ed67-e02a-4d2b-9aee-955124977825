((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_39",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var J,A,C,B={
Iw(){var x=0,w=A.i(y.b),v=1,u=[],t,s,r,q,p
var $async$Iw=A.d(function(d,e){if(d===1){u.push(e)
x=v}while(true)switch(x){case 0:v=3
s=C.bXP()
$.cms=s
p=$
x=6
return A.c(s.p7(0,"ImageCacheStorage",new B.aSE(),1),$async$Iw)
case 6:p.Iv=e
v=1
x=5
break
case 3:v=2
q=u.pop()
t=A.E(q)
A.W("IndexedDB \u521d\u59cb\u5316\u5931\u8d25: "+A.m(t))
x=5
break
case 2:x=1
break
case 5:return A.f(null,w)
case 1:return A.e(u.at(-1),w)}})
return A.h($async$Iw,w)},
aSE:function aSE(){}}
J=c[1]
A=c[0]
C=c[74]
B=a.updateHolder(c[60],B)
var z=a.updateTypes(["aY(Az)"])
B.aSE.prototype={
$1(d){var x=d.gSi(d)
J.aBC(x,"storage")},
$S:z+0};(function inheritance(){var x=a.inherit
x(B.aSE,A.cX)})()
var y={b:A.G("~")};(function staticFields(){$.cms=null
$.Iv=null})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_39",e:"endPart",h:b})})($__dart_deferred_initializers__,"OBJOKolimwBNCTYwb79SHK2vGUc=");