((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_176",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var J,A,D,B={
a5W(d,e){return B.chl(d,e)},
chl(d,e){var x=0,w=A.i(y.e),v,u=2,t=[],s=[],r,q,p,o,n,m,l,k,j,i
var $async$a5W=A.d(function(f,g){if(f===1){t.push(g)
x=u}while(true)switch(x){case 0:m=!1
l=0
k=y.g
j=A.R(["html",d,"id",e],k,k)
case 3:if(!!0){x=4
break}if(!(!m&&l<5)){x=4
break}u=6
k=$.br
p=k==null?null:k.cV(0,"accessToken")
if(p==null)A.a6(A.az("accessToken\u4e0d\u5b58\u5728"))
o=C.kH()
if(o==null)A.a6(A.az("baseUrl\u4e0d\u5b58\u5728"))
x=9
return A.c(C.ch(o+"/api/5/savehtml?accessToken="+p,j),$async$a5W)
case 9:k=g
r=D.m.N(0,new A.B6(!1).AQ(k,0,null,!0))
if(J.C(r,"isSuccess"))m=!0
s.push(8)
x=7
break
case 6:u=5
i=t.pop()
q=A.E(i)
A.mm().$1(A.m(q))
s.push(8)
x=7
break
case 5:s=[2]
case 7:u=2;++l
x=s.pop()
break
case 8:x=3
break
case 4:if(!m)throw A.k(A.az("\u56de\u4f20\u4fe1\u606f\u5931\u8d25"))
v=m
x=1
break
case 1:return A.f(v,w)
case 2:return A.e(t.at(-1),w)}})
return A.h($async$a5W,w)}},C
J=c[1]
A=c[0]
D=c[2]
B=a.updateHolder(c[21],B)
C=c[90]
var z=a.updateTypes([])
var y={g:A.G("j"),e:A.G("y")}};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_176",e:"endPart",h:b})})($__dart_deferred_initializers__,"dnsE2BouBS5NB3jELKLMFU9855U=");