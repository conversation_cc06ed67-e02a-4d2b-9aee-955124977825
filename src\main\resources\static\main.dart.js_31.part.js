((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_31",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A,B,C={
dk(d){var y
if(d==null)return 0
if(A.kV(d))return d
if(typeof d=="number")return B.e.dv(d)
if(typeof d=="string"){y=A.j9(d,null)
return y==null?0:y}return 0}}
A=c[0]
B=c[2]
C=a.updateHolder(c[94],C)
var z=a.updateTypes([])};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_31",e:"endPart",h:b})})($__dart_deferred_initializers__,"H6q8zFkOACLIdvXneP23tPWSSrg=");