((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_91",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A,B={
c4H(){var y,x=$.br
x=x==null?null:x.cV(0,"setting:cachebook")
y=A.dk(x==null?5:x)
return y<0?0:y}}
A=c[94]
B=a.updateHolder(c[46],B)
var z=a.updateTypes([])};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_91",e:"endPart",h:b})})($__dart_deferred_initializers__,"bf/9ZZafz9JEDMFJUuWOBd7r98Y=");