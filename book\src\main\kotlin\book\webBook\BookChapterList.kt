package book.webBook

import book.model.Book
import book.model.BookChapter
import book.model.BookSource
import book.model.rule.TocRule
import book.util.GSON
import book.util.TextUtils
import book.util.fromJsonArray
import book.util.fromJsonObject
import book.util.isJson
import book.util.isJsonObject
import book.util.isTrue
import book.webBook.analyzeRule.AnalyzeRule
import book.webBook.analyzeRule.AnalyzeUrl
import book.webBook.exception.TocEmptyException
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext

object BookChapterList {

    suspend fun analyzeChapterList(
        book: Book,
        body: String?,
        bookSource: BookSource,
        baseUrl: String,
        redirectUrl: String,
        debugLog: DebugLog? = null
    ): List<BookChapter> {
        body ?: throw Exception(
//            App.INSTANCE.getString(R.string.error_get_web_content, baseUrl)
            //todo getString
            "error_get_web_content"
        )
        val chapterList = arrayListOf<BookChapter>()
        debugLog?.log(bookSource.bookSourceUrl, "≡获取成功:${baseUrl}")
        // debugLog?.log(bookSource.bookSourceUrl, body)
        val tocRule = bookSource.getTocRule()
        val nextUrlList = arrayListOf(redirectUrl)
        var reverse = false
        var listRule = tocRule.chapterList ?: ""
        if (listRule.startsWith("-")) {
            reverse = true
            listRule = listRule.substring(1)
        }
        if (listRule.startsWith("+")) {
            listRule = listRule.substring(1)
        }
        var chapterData =
            analyzeChapterList(
                book, baseUrl, redirectUrl, body,
                tocRule, listRule, bookSource, true, true, debugLog
            )
        chapterList.addAll(chapterData.first)
        when (chapterData.second.size) {
            0 -> Unit
            1 -> {
                var nextUrl = chapterData.second[0]
                while (nextUrl.isNotEmpty() && !nextUrlList.contains(nextUrl)) {
                    nextUrlList.add(nextUrl)
                    AnalyzeUrl(
                        mUrl = nextUrl,
                        source = bookSource,
                        ruleData = book,
                        headerMapF = bookSource.getHeaderMap(),debugLog = debugLog
                    ).getStrResponseAwait().body?.let { nextBody ->
                        chapterData = analyzeChapterList(
                            book, nextUrl, nextUrl,
                            nextBody, tocRule, listRule, bookSource, true, false, debugLog
                        )
                        nextUrl = chapterData.second.firstOrNull() ?: ""
                        chapterList.addAll(chapterData.first)
                    }
                }
                debugLog?.log(bookSource.bookSourceUrl, "◇目录总页数:${nextUrlList.size}")
            }
            else -> {
                debugLog?.log(bookSource.bookSourceUrl, "◇并发解析目录,总页数:${chapterData.second.size}")
                withContext(IO) {
                    val asyncArray = Array(chapterData.second.size) {
                        async(IO) {
                            val urlStr = chapterData.second[it]
                            val analyzeUrl = AnalyzeUrl(
                                mUrl = urlStr,
                                source = bookSource,
                                ruleData = book,
                                headerMapF = bookSource.getHeaderMap(),debugLog = debugLog
                            )
                            val res = analyzeUrl.getStrResponseAwait()
                            analyzeChapterList(
                                book, urlStr, res.url,
                                res.body!!, tocRule, listRule, bookSource, false, false, debugLog
                            ).first
                        }
                    }
                    asyncArray.forEach { coroutine ->
                        chapterList.addAll(coroutine.await())
                    }
                }
            }
        }
        if (chapterList.isEmpty()) {
            throw TocEmptyException("目录为空")
        }
        //去重
        if (!reverse) {
            chapterList.reverse()
        }
        val lh = LinkedHashSet(chapterList)
        val list = ArrayList(lh)
        // if (!book.getReverseToc()) {
        list.reverse()
        // }
        debugLog?.log(book.origin, "◇目录总数:${list.size}")
        list.forEachIndexed { index, bookChapter ->
            bookChapter.index = index
        }
        if (list.size > 0) {
            book.latestChapterTitle = list.last().title
        }
//        book.durChapterTitle =
//            list.getOrNull(book.durChapterIndex)?.title ?: book.latestChapterTitle
        if (book.totalChapterNum < list.size) {
            book.lastCheckCount = list.size - book.totalChapterNum
            // book.latestChapterTime = System.currentTimeMillis()
            // book.lastCheckTime = System.currentTimeMillis()
        }
        book.totalChapterNum = list.size
        return list
    }

    private fun analyzeChapterList(
        book: Book,
        baseUrl: String,
        redirectUrl: String,
        body: String,
        tocRule: TocRule,
        listRule: String,
        bookSource: BookSource,
        getNextUrl: Boolean = true,
        log: Boolean = false,
        debugLog: DebugLog? = null
    ): Pair<List<BookChapter>, List<String>>  {
        val analyzeRule = AnalyzeRule(book,debugLog, bookSource,)
        analyzeRule.setContent(body).setBaseUrl(baseUrl)
        analyzeRule.setRedirectUrl(redirectUrl)
        //获取目录列表
        val chapterList = arrayListOf<BookChapter>()
        if(log) debugLog?.log(bookSource.bookSourceUrl, "┌获取目录列表")
        val elements = analyzeRule.getElements(listRule)
        if(log) debugLog?.log(bookSource.bookSourceUrl, "└列表大小:${elements.size}")
        //获取下一页链接
        val nextUrlList = arrayListOf<String>()
        val nextTocRule = tocRule.nextTocUrl
        if (getNextUrl && !nextTocRule.isNullOrEmpty()) {
            if(log) debugLog?.log(bookSource.bookSourceUrl, "┌获取目录下一页列表")
            analyzeRule.getStringList(nextTocRule, isUrl = true)?.let {
                for (item in it) {
                    if (item != redirectUrl) {
                        nextUrlList.add(item)
                    }
                }
            }
            if(log) debugLog?.log(bookSource.bookSourceUrl, "└" + TextUtils.join("，\n", nextUrlList))
        }
        if (elements.isNotEmpty()) {
            if(log) debugLog?.log(bookSource.bookSourceUrl, "┌解析目录列表")
            val nameRule = analyzeRule.splitSourceRule(tocRule.chapterName)
            val urlRule = analyzeRule.splitSourceRule(tocRule.chapterUrl)
            val vipRule = analyzeRule.splitSourceRule(tocRule.isVip)
            val payRule = analyzeRule.splitSourceRule(tocRule.isPay)
            //val wordCount = analyzeRule.splitSourceRule(tocRule.wordCount)
            val upTimeRule = analyzeRule.splitSourceRule(tocRule.updateTime)
            val isVolumeRule = analyzeRule.splitSourceRule(tocRule.isVolume)
            elements.forEachIndexed { index, item ->
                analyzeRule.setContent(item)
                val bookChapter = BookChapter(bookUrl = book.bookUrl, baseUrl = redirectUrl, userid = bookSource.userid?:"")
                analyzeRule.chapter = bookChapter
                bookChapter.title = analyzeRule.getString(nameRule)
                bookChapter.url = analyzeRule.getString(urlRule)
                bookChapter.tag = analyzeRule.getString(upTimeRule)
                //bookChapter.wordCount= analyzeRule.getString(wordCount)
                val isVolume = analyzeRule.getString(isVolumeRule)
                bookChapter.isVolume = false
                if (isVolume.isTrue()) {
                    bookChapter.isVolume = true
                }
                if (bookChapter.url.isEmpty()) {
                    if (bookChapter.isVolume) {
                        bookChapter.url = bookChapter.title + index
                        if(log) debugLog?.log(bookSource.bookSourceUrl, "⇒一级目录${index}未获取到url,使用标题替代")
                    } else {
                        bookChapter.url = baseUrl
                        if(log) debugLog?.log(bookSource.bookSourceUrl, "⇒目录${index}未获取到url,使用baseUrl替代")
                    }
                }
                if (bookChapter.title.isNotEmpty()) {
                    val isVip = analyzeRule.getString(vipRule)
                    bookChapter.isVip= isVip.isTrue()
                    if (isVip.isTrue()) {
                        bookChapter.title = "\uD83D\uDD12" + bookChapter.title
                    }
                    val isPay = analyzeRule.getString(payRule)
                    bookChapter.isPay= isPay.isTrue()
                    chapterList.add(bookChapter)
                }
            }
            if(log) debugLog?.log(bookSource.bookSourceUrl, "└目录列表解析完成")
            if(log) debugLog?.log(bookSource.bookSourceUrl, "≡首章信息")
            if(log) debugLog?.log(bookSource.bookSourceUrl, "◇章节名称:${chapterList[0].title}")
            if(log) debugLog?.log(bookSource.bookSourceUrl, "◇章节链接:${chapterList[0].url}")
            if(log) debugLog?.log(bookSource.bookSourceUrl, "◇章节信息:${chapterList[0].tag}")
            if(log) debugLog?.log(bookSource.bookSourceUrl, "◇是否卷名:${chapterList[0].isVolume}")
        }
        return Pair(chapterList, nextUrlList)
    }

}