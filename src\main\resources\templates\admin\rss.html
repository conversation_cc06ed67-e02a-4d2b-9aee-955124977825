

<#include "header.html">


<div style="margin-left: 8%;margin-right: 8%; margin-top: 40px">
  <div style="margin-left: 0px">
    <form class="layui-form" action="">
      <div class="layui-form-item">
        <div class="layui-inline"  style="margin-left: 0px">
          <div class="layui-input-block" style="width: 800px">
            <input type="text" name="where" style="width: 100%" autocomplete="off" placeholder="搜索条件" class="layui-input">
          </div>
        </div>
        <div class="layui-inline width30">
          <div class="layui-input-block">
            <button class="layui-btn" lay-submit="" lay-filter="InZDSeach" style="margin-left: 10px;">搜索</button>
            <button type="button" id="upload" class="layui-btn" lay-options="{acceptMime: 'application/json'}">
              <i class="layui-icon layui-icon-upload"></i>
              上传订阅源
            </button>
          </div>
        </div>
      </div>
    </form>

  </div>
  <div>
    <div>

      <table lay-filter="InZDSeach" id="InZDSeach">
        <thead>
        <tr>
          <th lay-data="{type: 'checkbox', fixed: 'left'}"></th>
          <th lay-data="{title:'', width:50,templet: '#indexTpl'}"></th>
          <th lay-data="{field:'sourceName', title:'名称', width:200}"></th>
          <th lay-data="{field:'sourceUrl', title:'地址'}"></th>
          <th lay-data="{field:'sourceGroup', title:'分组'}"></th>
          <th lay-data="{field:'enabled', title:'状态',templet: '#enabled'}"></th>
          <th lay-data="{field:'sourceComment', title:'备注'}"></th>
          <th lay-data="{title:'操作' ,templet: '#tool' , width:250}"></th>
        </tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
  </div>
</div>

<script type="text/html" id="indexTpl">
  {{ d.LAY_INDEX+1}}
</script>


<script type="text/html" id="enabled">
  {{#  if(d.enabled){ }}
  <span class="layui-badge layui-bg-blue">启用</span>
  {{#  } else { }}
  <span class="layui-badge layui-bg-orange">禁止</span>
  {{#  } }}
</script>


<script type="text/html" id="toolbar">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="alldel">批量删除</button>
    <button class="layui-btn layui-btn-sm" lay-event="dao">批量导出</button>
  </div>
</script>

<script type="text/html" id="tool">
  <div class="layui-clear-space">
    {{#  if(!d.enabled){ }}
    <a class="layui-btn layui-btn-xs" lay-event="start">启用</a>
    {{#  } else { }}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="stop">禁止</a>
    {{#  } }}
    <a class="layui-btn layui-btn-xs" lay-event="top">置顶</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
  </div>
</script>

<script src="/static/layui/layui.js"></script>
<script src="/static/js/ajax.js"></script>
<script>
  var $
  layui.use(['jquery','table','form', 'dropdown'], function () {
    var form =layui.form,
            table = layui.table
    var dropdown = layui.dropdown;
    var where="",order = 'sourceorder asc'
    $ = layui.$

    var upload = layui.upload;
    var layer = layui.layer;
    // 渲染
    upload.render({
      elem: '#upload', // 绑定多个元素
      url: '/admin/uploadRssSource', // 此处配置你自己的上传接口即可
      accept: 'file', // 普通文件
      done: function(res){
        layer.msg(res.errorMsg);
        console.log(res);
        shua()
      }
    });
    $('#debug').click(function(){
      window.open("/bookSourceDebug/index.html", "_blank");

    })
    //$("#stateselect").val("0");

    table.init('InZDSeach', {
      page: true
      , toolbar: '#toolbar'
      , url:  '/admin/seachrssSource'
      , where: {where: where,order:order}
      , limits: [10, 30, 50,100,150,200,250,300,350,400]
      , loading :true
    });
    table.on('toolbar(InZDSeach)', function(obj){
      var id = obj.config.id;
      var checkStatus = table.checkStatus(id);
      var othis = lay(this);
      switch(obj.event){
        case 'alldel':
          var data = checkStatus.data;
          var  ids=[]
          data.forEach(d => {
            ids.push(d.sourceUrl)
          })
          console.log(ids)
          if(ids.length>0){
            layer.confirm('确认批量删除这些书源吗？', function(index){
              ajaxjson('/admin/delRssSources',ids,function (data) {
                if (data.isSuccess == true){
                  shua()
                  layer.close(index);
                  layer.msg("删除成功");
                }else{
                  layer.msg(data.errorMsg);
                  layer.close(index);
                }
              })
            });
          }else{
            layer.msg("请选择订阅源");
          }

          break;
        case 'dao':
          var data = checkStatus.data;
          var  ids=[]
          data.forEach(d => {
            ids.push(JSON.parse(d.json))
          })
          console.log(ids)
          if(ids.length>0){
            const stringData = JSON.stringify(ids)
            // dada 表示要转换的字符串数据，type 表示要转换的数据格式
            const blob = new Blob([stringData], {
              type: 'application/json'
            })
            // 根据 blob生成 url链接
            const objectURL = URL.createObjectURL(blob)

            // 创建一个 a 标签Tag
            const aTag = document.createElement('a')
            // 设置文件的下载地址
            aTag.href = objectURL
            // 设置保存后的文件名称
            aTag.download = "read"+new Date().getTime()+".json"
            // 给 a 标签添加点击事件
            aTag.click()
            // 释放一个之前已经存在的、通过调用 URL.createObjectURL() 创建的 URL 对象。
            // 当你结束使用某个 URL 对象之后，应该通过调用这个方法来让浏览器知道不用在内存中继续保留对这个文件的引用了。
            URL.revokeObjectURL(objectURL)
          }else{
            layer.msg("请选择订阅源");
          }

          break;
      };
    });

    window.shua=function (){
      table.reload('InZDSeach', {
        page: {curr: 1}
        , where: {where: where,order:order}
      });
    }

    form.on('submit(InZDSeach)', function (data) {
      where = data.field.where;
      table.reload('InZDSeach', {
        page: {curr: 1}
        , where: {where: where,order:order}
      });
      return false
    });

    table.on('tool(InZDSeach)', function (obj) {
      var data = obj.data;
     if (obj.event === 'stop') {
        layer.confirm('真的停用 ['+ data.sourceName +'] 么', function(index){
          ajax("POST",'/admin/stopRssSource?st=0',{id:data.sourceUrl},function (data) {
            if (data.isSuccess == true){
              layer.close(index);
              layer.msg("操作成功");
              shua()
            }else{
              layer.msg(data.errorMsg);
              layer.close(index);
            }
          })
        });
      }else   if (obj.event === 'start') {
        layer.confirm('真的启用 ['+ data.sourceName +'] 么', function(index){
          ajax("POST",'/admin/stopRssSource?st=1',{id:data.sourceUrl},function (data) {
            if (data.isSuccess == true){
              layer.close(index);
              layer.msg("操作成功");
              shua()
            }else{
              layer.msg(data.errorMsg);
              layer.close(index);
            }
          })
        });
      }else  if (obj.event === 'del') {
        layer.confirm('真的删除 ['+ data.sourceName +'] 么', function(index){
          ajax("POST",'/admin/delRssSource',{id:data.sourceUrl},function (data) {
            if (data.isSuccess == true){
              obj.del(); // 删除对应行（tr）的DOM结构
              layer.close(index);
              layer.msg("删除成功");
            }else{
              layer.msg(data.errorMsg);
              layer.close(index);
            }
          })
        });
      }else  if (obj.event === 'top') {
        layer.confirm('真的将 ['+ data.bookSourceName +'] 置顶么', function(index){
          ajax("POST",'/admin/topRssSource',{id:data.sourceUrl},function (data) {
            if (data.isSuccess == true){
              layer.close(index);
              layer.msg("置顶成功");
              shua()
            }else{
              layer.msg(data.errorMsg);
              layer.close(index);
            }
          })
        });
      }


    });



  })
</script>

</body>
</html>