package book.model

import book.util.*
import book.util.help.CacheManager
import book.util.help.RuleBigDataHelp
import book.webBook.localBook.*
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.google.gson.annotations.Expose
import org.jsoup.Jsoup
import java.io.File
import java.nio.charset.Charset
import kotlin.math.max
import kotlin.math.min

@JsonIgnoreProperties("variableMap", "infoHtml", "tocHtml", "config", "rootDir", "readConfig", "localBook", "epub", "epubRootDir", "onLineTxt", "localTxt", "umd", "realAuthor", "unreadChapterNum", "folderName", "localFile", "kindList", "_userNameSpace", "bookDir", "userNameSpace")
data class Book(
    override var bookUrl: String = "",     // 详情页Url(本地书源存储完整文件路径)
    var tocUrl: String = "",                    // 目录页Url (toc=table of Contents)
    var origin: String = BookType.local,        // 书源URL(默认BookType.local)
    var originName: String = "",                //书源名称
    override var name: String = "",                   // 书籍名称(书源获取)
    override var author: String = "",                 // 作者名称(书源获取)
    override var kind: String? = null,                    // 分类信息(书源获取)
    var customTag: String? = null,              // 分类信息(用户修改)
    var coverUrl: String? = null,               // 封面Url(书源获取)
    var customCoverUrl: String? = null,         // 封面Url(用户修改)
    var intro: String? = null,            // 简介内容(书源获取)
    var customIntro: String? = null,      // 简介内容(用户修改)
    var charset: String? = null,                // 自定义字符集名称(仅适用于本地书籍)
    var type: Int = 0,                          // @BookType
    var group: Int = 0,                         // 自定义分组索引号
    var latestChapterTitle: String? = null,     // 最新章节标题
    var latestChapterTime: Long = System.currentTimeMillis(),            // 最新章节标题更新时间
    var lastCheckTime: Long = System.currentTimeMillis(),                // 最近一次更新书籍信息的时间
    var lastCheckCount: Int = 0,                // 最近一次发现新章节的数量
    var totalChapterNum: Int = 0,               // 书籍目录总数
    var durChapterTitle: String? = null,        // 当前章节名称
    var durChapterIndex: Int = 0,               // 当前章节索引
    var durChapterPos: Int = 0,                 // 当前阅读的进度(首行字符的索引位置)
    var durChapterTime: Long = System.currentTimeMillis(),               // 最近一次阅读书籍的时间(打开正文的时间)
    override var wordCount: String? = null,
    var canUpdate: Boolean = true,              // 刷新书架时更新书籍信息
    var order: Int = 0,                         // 手动排序
    var originOrder: Int = 0,                   //书源排序
    var useReplaceRule: Boolean = true,         // 正文使用净化替换规则
    @Expose(serialize = false, deserialize = false)
    override var variable: String? = null,                // 自定义书籍变量信息(用于书源规则检索书籍信息)
    var readConfig: ReadConfig? = null,
    override var userid: String = ""
): BaseBook  {



    override fun toString(): String {
        val hashCode = this.hashCode()
        val hexHash = Integer.toHexString(hashCode)
        val s="io.legado.app.data.entities.Book@"+hexHash
        return s
    }

    companion object {
        const val hTag = 2L
        const val rubyTag = 4L
        const val imgTag = 8L
        const val imgStyleDefault = "DEFAULT"
        const val imgStyleFull = "FULL"
        const val imgStyleText = "TEXT"

        fun initLocalBook(bookUrl: String, localPath: String, rootDir: String = ""): Book {
            val fileName = File(localPath).name
            val nameAuthor = LocalBook.analyzeNameAuthor(fileName)
            val book = Book(bookUrl, "", BookType.local, localPath, nameAuthor.first, nameAuthor.second).also {
                it.canUpdate = false
            }
            book.setRootDir(rootDir)
            book.updateFromLocal()
            return book
        }
    }

    fun isLocalBook(): Boolean {
        return origin == BookType.local
    }

    fun isLocalTxt(): Boolean {
        return isLocalBook() && originName.endsWith(".txt", true)
    }

    fun isLocalEpub(): Boolean {
        return isLocalBook() && originName.endsWith(".epub", true)
    }

    fun isEpub(): Boolean {
        return originName.endsWith(".epub", true)
    }

    fun isCbz(): Boolean {
        return originName.endsWith(".cbz", true)
    }

    fun isUmd(): Boolean {
        return originName.endsWith(".umd", true)
    }

    fun isOnLineTxt(): Boolean {
        return !isLocalBook() && type == 0
    }

    override fun equals(other: Any?): Boolean {
        if (other is Book) {
            return other.bookUrl == bookUrl
        }
        return false
    }

    override fun hashCode(): Int {
        return bookUrl.hashCode()
    }

    @delegate:Expose(serialize = false, deserialize = false)
    override val variableMap: HashMap<String, String> by lazy {
        GSON.fromJsonObject<HashMap<String, String>>(variable).getOrNull() ?: hashMapOf()
    }

    @Expose(serialize = false, deserialize = false)
    override var infoHtml: String? = null

    @Expose(serialize = false, deserialize = false)
    override var tocHtml: String? = null

    fun getRealAuthor() = author.replace(AppPattern.authorRegex, "")

    fun getUnreadChapterNum() = max(totalChapterNum - durChapterIndex - 1, 0)

    fun getDisplayCover() = if (customCoverUrl.isNullOrEmpty()) coverUrl else customCoverUrl

    fun getDisplayIntro() = if (customIntro.isNullOrEmpty()) intro else customIntro

    fun fileCharset(): Charset {
        return charset(charset ?: "UTF-8")
    }


    private fun config(): ReadConfig {
        if (readConfig == null) {
            readConfig = ReadConfig()
        }
        return readConfig!!
    }

    fun setDelTag(tag: Long) {
        config().delTag =
            if ((config().delTag and tag) == tag) config().delTag and tag.inv() else config().delTag or tag
    }

    fun getDelTag(tag: Long): Boolean {
        return config().delTag and tag == tag
    }


    fun getFolderName(): String {
        //防止书名过长,只取9位
        var folderName = name.replace(AppPattern.fileNameRegex, "")
        folderName = folderName.substring(0, min(9, folderName.length))
        return folderName + MD5Utils.md5Encode16(bookUrl)
    }

    fun getLocalFile(): File {
//        if (isEpub() && originName.indexOf("localStore") < 0 && originName.indexOf("webdav") < 0) {
//            // 非本地/webdav书仓的 epub文件
//            return FileUtils.getFile(File(rootDir + originName), "index.epub")
//        }
//        if (isCbz() && originName.indexOf("localStore") < 0 && originName.indexOf("webdav") < 0) {
//            // 非本地/webdav书仓的 cbz文件
//            return FileUtils.getFile(File(rootDir + originName), "index.cbz")
//        }
//        println(rootDir + originName)
//        return File(rootDir + originName)
        return File( originName)
    }

    @Expose(serialize = false, deserialize = false)
    @Transient
    private var rootDir: String = ""

    fun setRootDir(root: String) {
        if (root.isNotEmpty() && !root.endsWith(File.separator)) {
            rootDir = root + File.separator
        } else {
            rootDir = root
        }
    }

    @Expose(serialize = false, deserialize = false)
    @Transient
    private var _userNameSpace: String = ""

    fun setUserNameSpace(nameSpace: String) {
        _userNameSpace = nameSpace
    }

    fun getUserNameSpace(): String {
        return _userNameSpace
    }

    fun getBookDir(): String {
        return FileUtils.getPath(File(rootDir), "storage", "data", _userNameSpace, name + "_" + author)
    }

    fun getSplitLongChapter(): Boolean {
        return false
    }


    fun toSearchBook(): SearchBook {
        return SearchBook(
            name = name,
            author = author,
            kind = kind,
            bookUrl = bookUrl,
            origin = origin,
            originName = originName,
            type = type,
            wordCount = wordCount,
            latestChapterTitle = latestChapterTitle,
            coverUrl = coverUrl,
            intro = intro,
            tocUrl = tocUrl,
//                originOrder = originOrder,
           // variable = variable
        ).apply {
            this. userid = userid
            this.variable = variable
            this.infoHtml = <EMAIL>
            this.tocHtml = <EMAIL>
        }
    }

    fun getEpubRootDir(): String {
        // 根据 content.opf 位置来确认root目录
        // var contentOPF = "OEBPS/content.opf"

        val defaultPath = "OEBPS"

        // 根据 META-INF/container.xml 来获取 contentOPF 位置
        val containerRes = File(bookUrl + File.separator + "index" + File.separator + "META-INF" + File.separator + "container.xml")
        if (containerRes.exists()) {
            try {
                val document = Jsoup.parse(containerRes.readText())
                val rootFileElement = document
                    .getElementsByTag("rootfiles").get(0)
                    .getElementsByTag("rootfile").get(0);
                val result = rootFileElement.attr("full-path");
                if (result != null && result.isNotEmpty()) {
                    return File(result).parentFile?.let{
                        it.toString()
                    } ?: ""
                }
            } catch (e: Exception) {
                e.printStackTrace();
                // Log.e(TAG, e.getMessage(), e);
            }
        }

        // 返回默认位置
        return defaultPath
    }

    fun updateFromLocal(onlyCover: Boolean = false) {
        try {
            if (isEpub()) {
                EpubFile.upBookInfo(this, onlyCover)
            } else if (isUmd()) {
                UmdFile.upBookInfo(this, onlyCover)
            } else if (isCbz()) {
                CbzFile.upBookInfo(this, onlyCover)
            }
        } catch(e: Exception) {
            e.printStackTrace()
        }
    }

    fun workRoot(): String {
        return rootDir
    }

    data class ReadConfig(
        var reverseToc: Boolean = false,
        var pageAnim: Int = -1,
        var reSegment: Boolean = false,
        var imageStyle: String? = null,
        var useReplaceRule: Boolean = false,   // 正文使用净化替换规则
        var delTag: Long = 0L   //去除标签
    )

    class Converters {
        fun readConfigToString(config: ReadConfig?): String = GSON.toJson(config)

        fun stringToReadConfig(json: String?) = GSON.fromJsonObject<ReadConfig>(json)
    }

}