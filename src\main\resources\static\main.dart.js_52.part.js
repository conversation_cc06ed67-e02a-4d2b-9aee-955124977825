((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_52",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var B,A={aKQ:function aKQ(){this.b=this.a=null}}
B=c[0]
A=a.updateHolder(c[36],A)
A.aKQ.prototype={}
var z=a.updateTypes([]);(function inheritance(){var y=a.inherit
y(A.aKQ,B.w)})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_52",e:"endPart",h:b})})($__dart_deferred_initializers__,"NcrhTgahaWm1ZWWcQhBH4xF/NsA=");