((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_38",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A,B={
aSF(){var x=0,w=A.i(y.b)
var $async$aSF=A.d(function(d,e){if(d===1)return A.e(e,w)
while(true)switch(x){case 0:x=2
return A.c(C.Iw(),$async$aSF)
case 2:return A.f(null,w)}})
return A.h($async$aSF,w)}},C
A=c[0]
B=a.updateHolder(c[13],B)
C=c[60]
var z=a.updateTypes([])
var y={b:A.G("~")}};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_38",e:"endPart",h:b})})($__dart_deferred_initializers__,"/fAjg0dfcyI+Avc7FTlAlvhUVEk=");