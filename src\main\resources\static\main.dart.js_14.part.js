((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_14",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var J,A={
cn3(d,e){var y,x
for(y=J.bf(d);y.B();){x=y.gS(y)
if(e.$1(x))return x}return null}}
J=c[1]
A=a.updateHolder(c[50],A)
var z=a.updateTypes([])};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_14",e:"endPart",h:b})})($__dart_deferred_initializers__,"yT5cftCV2p43mIpYoPYoUww/pZM=");