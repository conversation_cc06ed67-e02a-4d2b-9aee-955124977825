((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_140",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var B,D,A={SC:function SC(d,e){this.a=d
this.b=e},bdP:function bdP(){},aG_:function aG_(){},
cBE(d){switch(d.a){case 0:return D.qH
case 2:return D.MD
case 1:return D.MC
case 3:return C.alr
case 4:return D.ME}},
o9(d,e){return A.cDb(d,e)},
cDb(d,e){var x=0,w=B.i(y.a),v,u
var $async$o9=B.d(function(f,g){if(f===1)return B.e(g,w)
while(true)switch(x){case 0:if(e===C.a3y||e===C.a3z)u=!(d.geU()==="https"||d.geU()==="http")
else u=!1
if(u)throw B.k(B.ei(d,"url","To use an in-app web view, you must provide an http(s) URL."))
v=$.cdT().Kv(d.j(0),new B.acq(A.cBE(e),new B.abQ(!0,!0,D.mj),null))
x=1
break
case 1:return B.f(v,w)}})
return B.h($async$o9,w)}},C
B=c[0]
D=c[2]
A=a.updateHolder(c[53],A)
C=c[117]
A.SC.prototype={
L(){return"LaunchMode."+this.b}}
A.bdP.prototype={}
A.aG_.prototype={}
var z=a.updateTypes([]);(function inheritance(){var x=a.inherit,w=a.inheritMany
x(A.SC,B.fM)
w(B.w,[A.bdP,A.aG_])})()
var y={a:B.G("y")};(function constants(){C.aAB=new A.aG_()
C.aAG=new A.bdP()
C.d8=new A.SC(0,"platformDefault")
C.a3y=new A.SC(1,"inAppWebView")
C.a3z=new A.SC(2,"inAppBrowserView")
C.alr=new B.Ea(3,"externalApplication")})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_140",e:"endPart",h:b})})($__dart_deferred_initializers__,"itVax9rqj+HParfUthBnTsII3rI=");