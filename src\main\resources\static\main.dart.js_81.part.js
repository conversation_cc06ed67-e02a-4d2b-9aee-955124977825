((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_81",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var J,A,C,B={
jP(d,e){return B.csq(d,e)},
csq(d,e){var x=0,w=A.i(y.b),v,u=2,t=[],s,r,q,p,o
var $async$jP=A.d(function(f,g){if(f===1){t.push(g)
x=u}while(true)switch(x){case 0:u=4
x=$.L8==null?7:8
break
case 7:x=9
return A.c(C.L9(),$async$jP)
case 9:case 8:s=$.L8.n1(0,"storage","readwrite")
r=J.rm(s,"storage")
x=10
return A.c(J.aBI(r,e,d),$async$jP)
case 10:x=11
return A.c(J.rk(s),$async$jP)
case 11:x=1
break
u=2
x=6
break
case 4:u=3
o=t.pop()
q=A.E(o)
A.W("Web\u4fdd\u5b58\u5931\u8d25: "+d+", \u9519\u8bef: "+A.m(q))
x=6
break
case 3:x=2
break
case 6:x=1
break
case 1:return A.f(v,w)
case 2:return A.e(t.at(-1),w)}})
return A.h($async$jP,w)}}
J=c[1]
A=c[0]
C=c[73]
B=a.updateHolder(c[68],B)
var z=a.updateTypes([])
var y={b:A.G("~")}};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_81",e:"endPart",h:b})})($__dart_deferred_initializers__,"ybN+VrrtQR7DFG6Q9Zc5uZw4OS8=");