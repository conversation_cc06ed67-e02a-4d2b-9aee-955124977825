((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_120",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A,D,B={
cA8(d){var x,w,v=A.a([],y.h)
for(x=d.length,w=0;w<d.length;d.length===x||(0,A.T)(d),++w)v.push(d[w].j(0))
return v},
Lj(d,e){return B.ctq(d,e)},
ctq(d,e){var x=0,w=A.i(y.f),v
var $async$Lj=A.d(function(f,g){if(f===1)return A.e(g,w)
while(true)switch(x){case 0:v=y.f
x=d!==C.Ot?2:4
break
case 2:x=5
return A.c(D.bR.dY("SystemChrome.setEnabledSystemUIMode",d.L(),v),$async$Lj)
case 5:x=3
break
case 4:e.toString
x=6
return A.c(D.bR.dY("SystemChrome.setEnabledSystemUIOverlays",B.cA8(e),v),$async$Lj)
case 6:case 3:return A.f(null,w)}})
return A.h($async$Lj,w)},
Xf:function Xf(d,e){this.a=d
this.b=e},
EY(){var x=0,w=A.i(y.f)
var $async$EY=A.d(function(d,e){if(d===1)return A.e(e,w)
while(true)switch(x){case 0:$.bXo=Date.now()
A.W("\u663e\u793a\u72b6\u6001\u680f")
$.bS4=!0
x=2
return A.c(B.Lj(C.apG,null),$async$EY)
case 2:return A.f(null,w)}})
return A.h($async$EY,w)}},C
A=c[0]
D=c[2]
B=a.updateHolder(c[47],B)
C=c[158]
B.Xf.prototype={
L(){return"SystemUiMode."+this.b}}
var z=a.updateTypes([]);(function inheritance(){var x=a.inherit
x(B.Xf,A.fM)})()
var y={h:A.G("t<j>"),f:A.G("~")};(function constants(){C.apG=new B.Xf(3,"edgeToEdge")
C.Ot=new B.Xf(4,"manual")})();(function staticFields(){$.bS4=!0
$.bXo=0})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_120",e:"endPart",h:b})})($__dart_deferred_initializers__,"y6+D7DP5Z/o12YsjGhN9ZnjSjGU=");