((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_55",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A={
cz9(){var y,x
try{y=$.ccI()
return y}catch(x){}return!1},
czq(){var y,x
try{y=$.a4w()
return y}catch(x){}return!1}}
A=a.updateHolder(c[85],A)
var z=a.updateTypes([]);(function staticFields(){$.it="0"})();(function lazyInitializers(){var y=a.lazyFinal,x=a.lazy
y($,"cJT","ccI",()=>{$.bSu()
return!1})
x($,"cPj","hM",()=>{A.cz9()
return!1})
x($,"cPl","eT",()=>{A.czq()
return!1})})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_55",e:"endPart",h:b})})($__dart_deferred_initializers__,"GSzJ/0VM1FEu8RVNE7Dm00VtipM=");