((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_22",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A={
a44(d,e){var y=null
return $.aBx().a5G(0,d,e,y,y,y,y,y,y,y,y,y,y,y,y,y,y)}}
A=a.updateHolder(c[78],A)
var z=a.updateTypes([])};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_22",e:"endPart",h:b})})($__dart_deferred_initializers__,"pun2h/W0oB11ZvhjIBte8eVxtHE=");