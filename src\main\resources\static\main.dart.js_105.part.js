((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_105",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A,B
A=c[144]
B=c[83]
var z=a.updateTypes([]);(function constants(){A.OC=new B.tT(1,"double")})()};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_105",e:"endPart",h:b})})($__dart_deferred_initializers__,"I2iSaAuRWaC9Mddfz1B5JA+pjdQ=");