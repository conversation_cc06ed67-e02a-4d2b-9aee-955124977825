{"assets/bg/1.jpg": ["assets/bg/1.jpg"], "assets/bg/10.jpg": ["assets/bg/10.jpg"], "assets/bg/11.jpg": ["assets/bg/11.jpg"], "assets/bg/12.jpg": ["assets/bg/12.jpg"], "assets/bg/13.jpg": ["assets/bg/13.jpg"], "assets/bg/14.jpg": ["assets/bg/14.jpg"], "assets/bg/2.jpg": ["assets/bg/2.jpg"], "assets/bg/3.jpg": ["assets/bg/3.jpg"], "assets/bg/4.jpg": ["assets/bg/4.jpg"], "assets/bg/5.jpg": ["assets/bg/5.jpg"], "assets/bg/6.jpg": ["assets/bg/6.jpg"], "assets/bg/7.jpg": ["assets/bg/7.jpg"], "assets/bg/8.jpg": ["assets/bg/8.jpg"], "assets/bg/9.jpg": ["assets/bg/9.jpg"], "assets/images/default_avatar.jpeg": ["assets/images/default_avatar.jpeg"], "assets/images/fq.png": ["assets/images/fq.png"], "assets/images/ic_launcher_round.webp": ["assets/images/ic_launcher_round.webp"], "assets/images/logo.png": ["assets/images/logo.png"], "assets/images/no_cover.jpeg": ["assets/images/no_cover.jpeg"], "assets/images/qd.png": ["assets/images/qd.png"], "assets/images/qq.png": ["assets/images/qq.png"], "assets/images/splash.png": ["assets/images/splash.png"], "fonts/boldface/AlimamaShuHeiTi-Bold.ttf": ["fonts/boldface/AlimamaShuHeiTi-Bold.ttf"], "fonts/circle/AlimamaFangYuanTiVF-Thin.ttf": ["fonts/circle/AlimamaFangYuanTiVF-Thin.ttf"], "packages/cupertino_icons/assets/CupertinoIcons.ttf": ["packages/cupertino_icons/assets/CupertinoIcons.ttf"], "packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css": ["packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css"], "packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html": ["packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html"], "packages/flutter_inappwebview_web/assets/web/web_support.js": ["packages/flutter_inappwebview_web/assets/web/web_support.js"], "packages/fluttertoast/assets/toastify.css": ["packages/fluttertoast/assets/toastify.css"], "packages/fluttertoast/assets/toastify.js": ["packages/fluttertoast/assets/toastify.js"], "packages/wakelock_plus/assets/no_sleep.js": ["packages/wakelock_plus/assets/no_sleep.js"]}