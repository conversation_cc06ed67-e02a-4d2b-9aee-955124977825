((a,b,c)=>{a[b]=a[b]||{}
a[b][c]=a[b][c]||[]
a[b][c].push({p:"main.dart.js_97",e:"beginPart"})})(self,"$__dart_deferred_initializers__","eventLog")
$__dart_deferred_initializers__.current=function(a,b,c,$){var A={
bVG(){var y=$.br
y=y==null?null:y.cV(0,"useReplaceRule")
return y!=="false"}}
A=a.updateHolder(c[33],A)
var z=a.updateTypes([])};
((a,b)=>{a[b]=a.current
a.eventLog.push({p:"main.dart.js_97",e:"endPart",h:b})})($__dart_deferred_initializers__,"sXCUW+ZwgU+IsuFiFXbc4TLccyo=");