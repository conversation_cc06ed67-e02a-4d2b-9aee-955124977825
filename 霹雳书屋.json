[{"bookSourceName": "霹雳书屋", "bookSourceGroup": "晋江完结", "loginUi": "", "bookSourceUrl": "https://www.pilishuwu.com", "bookSourceType": 0, "bookUrlPattern": "", "customOrder": 0, "enabled": true, "enabledExplore": true, "concurrentRate": "", "header": "@js:\nheaders = {\n\t\"User-Agent\": getua()\n}\nJSON.stringify(headers)", "loginUrl": "", "loginCheckJs": "src = result.body()\nif(src.match(/Tis/)){\n\tjava.toast('请不要频繁刷新，请休息几秒再试吧')\n}\nif(src.includes('Just a moment') || src.includes('请稍候…') ){\n\turl =  result.url();\ncookie.setCookie(url,\"\");\n\tresult=java.startBrowserAwait(url,\"验证\",false)\n}\nresult", "lastUpdateTime": 0, "weight": 0, "exploreUrl": "[\n{\n    \"title\": \"♕ 书库 ♕\",\n    \"url\": \"\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 1\n    }\n  },\n  {\n    \"title\": \"全部小说\",\n    \"url\": \"/0/list/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"男频网文\",\n    \"url\": \"/1/list/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"女频言情\",\n    \"url\": \"/2/list/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"电子图书\",\n    \"url\": \"/3/list/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"无CP小说\",\n    \"url\": \"/4/list/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"纯爱小说\",\n    \"url\": \"/5/list/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"百合小说\",\n    \"url\": \"/6/list/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"轻小说\",\n    \"url\": \"/8/list/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"♕ 排行 ♕\",\n    \"url\": \"\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 1\n    }\n  },\n  {\n    \"title\": \"最新入库\",\n    \"url\": \"/top/list/0/1/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"全部点击\",\n    \"url\": \"/top/list/0/2/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"本年点击\",\n    \"url\": \"/top/list/0/3/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"本月点击\",\n    \"url\": \"/top/list/0/4/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"本周点击\",\n    \"url\": \"/top/list/0/5/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  },\n  {\n    \"title\": \"本日点击\",\n    \"url\": \"/top/list/0/6/{{page}}.html,{\\\"webView\\\": true}\",\n    \"style\": {\n      \"layout_flexGrow\": 1,\n      \"layout_flexBasisPercent\": 0.25\n    }\n  }\n]\n", "ruleExplore": {"bookList": ".x-book||.x-book", "name": ".x-book__info--name@text||.x-info__name@text", "author": ".x-book__info--author@ownText||span.1@text", "intro": ".x-book__info--chapter@text||.x-info__intro@text", "kind": ".red@text||.x-info__author@ownText", "lastChapter": "", "updateTime": "", "bookUrl": "a.0@href##$##,{\"webView\": true}", "coverUrl": "a.0@img@data-src", "wordCount": ""}, "searchUrl": "/search/0/{{key}}/{{page}}.html,{\"webView\": true}", "ruleSearch": {"bookList": ".x-book", "name": "p.0@text", "author": ".book-info-status@span.0@text", "intro": "p.-1@text", "kind": ".book-info-status@span.1@text", "lastChapter": "", "updateTime": "", "bookUrl": "a.0@href##$##,{\"webView\": true}", "coverUrl": "a.0@img@src", "wordCount": ".book-info-status@span.2@text", "checkKeyWord": ""}, "ruleBookInfo": {"init": "", "name": ".x-detail__info--title@text", "author": ".x-detail__info--author@text", "intro": ".x-detail__intro--content@html", "kind": ".x-detail__info--words@text&&.x-detail__info--reads@text##进程：|分类：", "lastChapter": ".x-catalog__new@ownText", "updateTime": "", "coverUrl": ".x-book__coverbox.0@img@src##$##,{\"headers\": {{getheader()}} }", "tocUrl": ".x-catalog__more@a@href##$##,{\"webView\": true}", "wordCount": "", "canReName": ""}, "ruleToc": {"preUpdateJs": "", "chapterList": ".chapter@a", "chapterName": "text", "chapterUrl": "href##.*/read/(\\d+).*##https://www.pilishuwu.com/wmcms/ajax/index.php?action=novel.getchapter&cid=$1&format=1,{\"webView\": true}", "isVolume": "", "isVip": "", "isPay": "", "wordCount": "", "updateTime": "", "nextTocUrl": ""}, "ruleContent": {"content": "$..content[*]", "nextContentUrl": "", "title": "", "webJs": "document.documentElement.textContent", "sourceRegex": "", "payAction": "", "replaceRegex": "", "imageStyle": ""}, "bookSourceComment": "//By情无羁(yesui.me),25.02.08,请勿频繁搜索,需等十秒\n// 轻阅读适配版\n备用网址(失效自行替换)：\nhttps://www.xiarishuwu.com\nhttps://www.pilibook.net", "respondTime": 180000, "jsLib": "function getua() {\n\tconst {java} = this\n    var u=\"\";\n    var ua=java.getWebViewUANEW();\n    if(ua.match(/iPhone/i)){\n        u=ua;\n    }else{\n        u=\"Mozilla/5.0 (Linux; U; Android 14; zh-cn; en-us; M2102K1AC Build/UKQ1.231207.002) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.118 Mobile Safari/537.36 MQQBrowser/10.1.0\"\n    }\n    return u;\n}\nfunction getheader() {\n\tconst {java,cookie,source} = this\n    var u=\"\";\n    var ua=java.getWebViewUANEW();\n    if(ua.match(/iPhone/i)){\n        u=ua;\n    }else{\n        u=\"Mozilla/5.0 (Linux; U; Android 14; zh-cn; en-us; M2102K1AC Build/UKQ1.231207.002) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.118 Mobile Safari/537.36 MQQBrowser/10.1.0\"\n    }\nheaders = {\n\t\"User-Agent\": u,\n\"Cookie\":cookie.getCookie(source.key),\n}\n    return JSON.stringify(headers);\n}", "enabledCookieJar": true, "userid": "", "variableComment": ""}]